const axios = require('axios');
const { token, apiKey, secret } = require('./config');

const headers = {
  Authorization: `Bearer ${token}`,
  'Content-Type': 'application/json',
  'x-api-key': apiKey,
  'x-secret': secret,
};

const BASE_URL = 'https://groww.in/v1/api';

async function getLTP(symbol='BSE_SENSEX') {
  const url = `https://api.groww.in/v1/live-data/ltp?segment=CASH&exchange_symbols=${symbol}`
  const res = await axios.get(url, { headers });
  console.log("data = ", res.data, res.data.payload.symbol);
  if (res?.data?.status !== 'SUCCESS' || !res?.data?.payload?.symbol) {
    console.log("Ltp not found");
    return null;
  }
  return res.data.payload.symbol;
}

async function placeOrder(symbol, side) {
    return;
  const url = 'https://groww.in/v1/api/option-chain-service/v1/order';
  const order = {
    symbol,
    side, // 'BUY'
    quantity: 1,
    order_type: 'MARKET',
    product: 'INTRADAY',
    exchange: 'NFO',
  };
  const res = await axios.post(url, order, { headers });
  return res.data;
}

async function getOrderDetail() {
  const url = `https://api.groww.in/v1/order/detail/GMKFO250731094525T96BIJYL0MFK?segment=FNO`;
  const res = await axios.get(url, { headers });
  console.log("data = ", res.data);
  return res.data;    
}

module.exports = {
  getLTP,
  placeOrder,
  getOrderDetail,
};
