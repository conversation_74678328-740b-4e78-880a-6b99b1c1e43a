const { getLTP, placeOrder } = require('./groww');
const { x, instrument, cooldownMinutes, timeSLMinutes, pollingIntervalMs } = require('./config');

let position = null;
let entryTime = null;
let lastExitTime = null;

function now() {
  return new Date();
}

function minutesSince(date) {
  return (Date.now() - date.getTime()) / 60000;
}

async function tradeLogic() {
  try {
    const ltp = await getLTP(instrument);
    console.log(`[${new Date().toLocaleTimeString()}] Price: ${ltp}`);

    // Cooldown check
    if (lastExitTime && minutesSince(lastExitTime) < cooldownMinutes) {
      return;
    }

    // Time stop check
    if (position && minutesSince(entryTime) > timeSLMinutes) {
      console.log(`⏱ Time SL hit — closing ${position}`);
      position = null;
      lastExitTime = now();
      return;
    }

    if (!position) {
      if (ltp > x) {
        console.log(`📈 Price broke above ${x}, buying CALL`);
        await placeOrder(`${instrument}_CALL`, 'BUY');
        position = 'CALL';
        entryTime = now();
      } else if (ltp < x) {
        console.log(`📉 Price broke below ${x}, buying PUT`);
        await placeOrder(`${instrument}_PUT`, 'BUY');
        position = 'PUT';
        entryTime = now();
      }
    } else {
      // Exit logic
      if ((position === 'CALL' && ltp <= x) || (position === 'PUT' && ltp >= x)) {
        console.log(`🔁 Exit condition met for ${position}`);
        position = null;
        lastExitTime = now();
      }
    }
  } catch (err) {
    console.error('❌ Error:', err.message);
  }
}

//setInterval(tradeLogic, pollingIntervalMs);
setInterval(() => getLTP(), 200);

//getLTP('NSE:TATAMOTORS');
