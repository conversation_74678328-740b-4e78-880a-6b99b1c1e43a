module.exports = {
  token: 'eyJraWQiOiJaTUtjVXciLCJhbGciOiJFUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.6C8XUMYmZuT5N8bvOWe9tqCQbVD7dE61JOnlMy-13lr3JnASUnh0u_MNT6Gvqi2zIl6FXDpzoAduV9t1IAleFA',
  apiKey: 'eyJraWQiOiJaTUtjVXciLCJhbGciOiJFUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FHSqV53jncY6aJGXMKsdkT9RZeoNPQ5uOfmf638U3JIKAVXu-GnA-Yz2T86xQN7K2QJCf0C5Qc8VhABsNiXNtQ',
 secret: '4HQFINJJKIQUHMNSRTHEKBDZRTKPBOZ7',
  instrument: 'RELIANCE',
  x: 3000, // Your reference price
  cooldownMinutes: 5,
  timeSLMinutes: 3,
  pollingIntervalMs: 5000,
};
